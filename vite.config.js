import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';
import vue from '@vitejs/plugin-vue';
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    monkey({
      entry: 'src/main.js',
      userscript: {
        name: 'Hupu Cleaner',
        match: ['https://bbs.hupu.com/*'],
        grant: ['GM_setValue', 'GM_getValue', 'GM_registerMenuCommand'],
        'run-at': 'document-start',
      },
    }),
  ],
  build: {
    minify: 'terser',
    terserOptions: {
      format: {
        comments: false,
      },
    },
  },
}); 