import { GM_getValue, GM_setValue } from '$';

export function getBlockedUsers() {
    return GM_getValue('blockedUsers', []).filter(u => u && typeof u === 'string') || [];
}

export function getBlockedImages() {
    return GM_getValue('blockedImages', []).filter(i => i && typeof i === 'string') || [];
}

export function saveBlockedUsers(users) {
    GM_setValue('blockedUsers', users);
}

export function saveBlockedImages(images) {
    GM_setValue('blockedImages', images);
}

export function addBlockedUser(username) {
    let currentBlockedUsers = getBlockedUsers();
    if (username && !currentBlockedUsers.includes(username)) {
        currentBlockedUsers.unshift(username);
        saveBlockedUsers(currentBlockedUsers);
    }
}

export function addBlockedImage(src) {
    let currentBlockedImages = getBlockedImages();
    if (src && !currentBlockedImages.includes(src)) {
        currentBlockedImages.unshift(src);
        saveBlockedImages(currentBlockedImages);
    }
} 