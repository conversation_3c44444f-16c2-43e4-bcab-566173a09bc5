<template>
    <div class="fixed inset-0 z-[99999999] flex items-center justify-center bg-black/30" v-if="isVisible">
        <div class="bg-white p-6 rounded-2xl shadow-2xl w-full max-w-[380px] min-h-[420px] flex flex-col overflow-hidden border border-gray-200" @click.stop>
            <div class="flex justify-between items-center border-b border-[#eee] pb-2 mb-4">
                <h3 class="text-lg font-bold text-center flex-1 text-gray-800">用户黑名单</h3>
                <button class="bg-none border-none text-2xl cursor-pointer text-[#888] hover:text-[#333] ml-2" @click="hideModal">×</button>
            </div>
            <div class="mb-2 text-xs text-gray-500 text-center">每行一个用户名，保存时自动去重</div>
            <textarea v-model="userListText" class="flex-1 resize-none overscroll-none rounded-md border-2 border-gray-300 p-2 text-[15px] outline-none focus:border-gray-400 bg-white font-mono mb-3" style="scrollbar-width: thin; scrollbar-color: #999 #0000; min-height: 160px;" spellcheck="false" placeholder="请输入要屏蔽的用户名..." />

            <!-- 紧凑布局开关 -->
            <div class="flex items-center justify-between mb-4 p-2 bg-gray-50 rounded-md">
                <span class="text-sm text-gray-700">紧凑布局</span>
                <button
                    @click="toggleCompactLayoutSetting"
                    :class="['relative inline-flex h-5 w-9 items-center rounded-full transition-colors', compactLayoutEnabled ? 'bg-blue-500' : 'bg-gray-300']"
                >
                    <span
                        :class="['inline-block h-3 w-3 transform rounded-full bg-white transition-transform', compactLayoutEnabled ? 'translate-x-5' : 'translate-x-1']"
                    ></span>
                </button>
            </div>
            <div class="flex justify-center gap-6 mt-2">
                <button :class="['w-24 rounded-md border-2 py-1 text-center text-base font-semibold transition', saveSuccess ? 'border-green-400 bg-green-50 hover:border-green-400 hover:bg-green-50' : 'border-gray-300 bg-white hover:border-blue-400 hover:bg-blue-50']" @click="saveUsers">保存</button>
                <button class="w-24 rounded-md border-2 border-gray-300 bg-white py-1 text-center text-base font-semibold hover:border-red-300 hover:bg-red-50" @click="hideModal">关闭</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { getBlockedUsers, saveBlockedUsers } from './storage.js';
import { restoreUserPosts, hidePosts } from './userBlocker.js';
import { toggleCompactLayout } from './layoutOptimizer.js';

const props = defineProps({
  hostElement: {
    type: Object,
    required: true,
  },
});

const isVisible = ref(false);
const userListText = ref('');
const saveSuccess = ref(false);
const compactLayoutEnabled = ref(false);

const loadBlockedData = () => {
  const users = getBlockedUsers();
  userListText.value = users.join('\n');
};

const showModal = () => {
  isVisible.value = true;
  loadBlockedData();
  // 检查当前紧凑布局状态
  compactLayoutEnabled.value = !!document.getElementById('hupu-compact-layout-style');
};

const hideModal = () => {
  isVisible.value = false;
  props.hostElement.style.setProperty('pointer-events', 'none', 'important');
  props.hostElement.style.setProperty('display', 'none', 'important');
};

const saveUsers = () => {
  const usersArray = userListText.value.split(/\n/).map(line => line.trim()).filter(line => line !== '');
  const uniqueUsers = [...new Set(usersArray)];

  // 获取当前已屏蔽的用户列表
  const currentBlockedUsers = getBlockedUsers();

  // 找出被删除的用户（在当前列表中但不在新列表中）
  const removedUsers = currentBlockedUsers.filter(user => !uniqueUsers.includes(user));

  // 保存新的用户列表
  saveBlockedUsers(uniqueUsers);
  userListText.value = uniqueUsers.join('\n');

  // 如果有用户被删除，恢复他们的内容
  if (removedUsers.length > 0) {
    restoreUserPosts(removedUsers);
  }

  // 重新隐藏当前屏蔽列表中的用户内容
  hidePosts();

  saveSuccess.value = true;
  setTimeout(() => saveSuccess.value = false, 1500);
};

// 切换紧凑布局设置
const toggleCompactLayoutSetting = () => {
  const newState = toggleCompactLayout();
  compactLayoutEnabled.value = newState;

  // 保存设置到本地存储
  localStorage.setItem('hupu-cleaner-compact-layout', newState ? 'enabled' : 'disabled');
};

defineExpose({ showModal, hideModal });
</script>
