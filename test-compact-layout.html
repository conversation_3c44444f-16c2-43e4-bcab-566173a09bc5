<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虎扑紧凑布局测试</title>
    <style>
        /* 模拟虎扑原始样式 */
        .bbs-sl-web-post-body {
            margin-bottom: 16px;
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            background: #fff;
        }
        
        .bbs-sl-web-post-layout {
            padding: 8px 0;
            line-height: 1.6;
        }
        
        .post-title {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .post-title .p-title {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            text-decoration: none;
        }
        
        .post-datum {
            font-size: 14px;
            margin: 4px 0;
            color: #666;
        }
        
        .post-auth, .post-time {
            font-size: 14px;
            line-height: 1.4;
            margin: 4px 0;
            color: #999;
        }
        
        .page-icon {
            font-size: 12px;
            margin-left: 8px;
            color: #666;
        }
        
        .light-icon {
            width: 16px;
            height: 16px;
            margin: 0 4px;
        }
        
        ul {
            list-style: none;
            margin: 0;
            padding: 20px;
        }
        
        /* 紧凑布局样式 - 更激进的压缩 */
        .compact-layout .bbs-sl-web-post-body {
            margin-bottom: 4px !important;
            padding: 4px 8px !important;
            line-height: 1.2 !important;
        }

        .compact-layout .bbs-sl-web-post-layout {
            padding: 2px 0 !important;
            line-height: 1.1 !important;
        }

        .compact-layout .post-title {
            margin-bottom: 2px !important;
            line-height: 1.2 !important;
        }

        .compact-layout .post-title .p-title {
            font-size: 13px !important;
            line-height: 1.2 !important;
        }

        .compact-layout .post-datum {
            font-size: 11px !important;
            margin: 1px 0 !important;
            line-height: 1.1 !important;
        }

        .compact-layout .post-auth, .compact-layout .post-time {
            font-size: 11px !important;
            line-height: 1.1 !important;
            margin: 1px 0 !important;
        }

        .compact-layout .page-icon {
            font-size: 10px !important;
            margin-left: 2px !important;
            line-height: 1.1 !important;
        }

        .compact-layout .light-icon {
            width: 10px !important;
            height: 10px !important;
            margin: 0 1px !important;
        }
        
        /* 控制按钮样式 */
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .toggle-btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .toggle-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="control-panel">
        <button class="toggle-btn" onclick="toggleCompactLayout()">切换紧凑布局</button>
        <div style="margin-top: 10px; font-size: 12px; color: #666;">
            当前状态: <span id="status">紧凑布局</span>
        </div>
    </div>

    <ul id="post-list" class="compact-layout">
        <li class="bbs-sl-web-post-body">
            <div class="bbs-sl-web-post-layout">
                <div class="post-title">
                    <a href="#" class="p-title">[流言板]继河南补贴后，湖南宣布对鸿蒙智行部分车型补贴3000元/台</a>
                </div>
                <div class="post-datum">40 / 23341</div>
                <div class="post-auth"><a href="#">虎扑汽车资讯</a></div>
                <div class="post-time">07-02 15:02</div>
            </div>
        </li>
        
        <li class="bbs-sl-web-post-body">
            <div class="bbs-sl-web-post-layout">
                <div class="post-title">
                    <a href="#" class="p-title">各位JR们觉得开一台2014年老保时捷macan在当下还有牌面吗？</a>
                </div>
                <div class="post-datum">8 / 489</div>
                <div class="post-auth"><a href="#">做人略微难啊</a></div>
                <div class="post-time">07-02 15:02</div>
            </div>
        </li>
        
        <li class="bbs-sl-web-post-body">
            <div class="bbs-sl-web-post-layout">
                <div class="post-title">
                    <a href="#" class="p-title">感觉新能源车还算保值的</a>
                </div>
                <div class="post-datum">25 / 21104</div>
                <div class="post-auth"><a href="#">TancenZ</a></div>
                <div class="post-time">07-02 15:02</div>
            </div>
        </li>
        
        <li class="bbs-sl-web-post-body">
            <div class="bbs-sl-web-post-layout">
                <div class="post-title">
                    <a href="#" class="p-title">分享下车贷银行提前还款的经历。</a>
                </div>
                <div class="post-datum">8 / 11076</div>
                <div class="post-auth"><a href="#">荒唐了一生</a></div>
                <div class="post-time">07-02 15:02</div>
            </div>
        </li>
        
        <li class="bbs-sl-web-post-body">
            <div class="bbs-sl-web-post-layout">
                <div class="post-title">
                    <a href="#" class="p-title">小米24万锁单相当于什么</a>
                    <span class="page-icon">[2]</span>
                </div>
                <div class="post-datum">48 / 14494</div>
                <div class="post-auth"><a href="#">自粘便条纸</a></div>
                <div class="post-time">07-02 15:01</div>
            </div>
        </li>
        
        <li class="bbs-sl-web-post-body">
            <div class="bbs-sl-web-post-layout">
                <div class="post-title">
                    <a href="#" class="p-title">理想汽车6月交付量36279辆 ，同比下降24%。</a>
                </div>
                <div class="post-datum">33 / 8583</div>
                <div class="post-auth"><a href="#">虎扑JR1347198559</a></div>
                <div class="post-time">07-02 15:01</div>
            </div>
        </li>
    </ul>

    <script>
        function toggleCompactLayout() {
            const postList = document.getElementById('post-list');
            const status = document.getElementById('status');

            if (postList.classList.contains('compact-layout')) {
                postList.classList.remove('compact-layout');
                status.textContent = '正常布局';
            } else {
                postList.classList.add('compact-layout');
                status.textContent = '紧凑布局';
            }
        }
    </script>
</body>
</html>
