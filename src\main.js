import { createApp } from 'vue';
// import App from './App.vue'; // 已删除
import { initializeScript } from './initializer.js';
import { GM_registerMenuCommand, unsafeWindow } from '$';
import modalCssContent from './modal.css?inline';
import './tailwind.css';
import './user-modal-entry.js';
// import './image-modal-entry.js'; // 移除图片模态框入口

// 在页面内容渲染之前，立即注入全局隐藏样式，避免闪烁。
(function() {
    const style = document.createElement('style');
    style.textContent = `
        .hupu-cleaner-hidden {
            display: none !important; /* 强制隐藏帖子和图片 */
        }
    `;
    // 确保 head 元素存在，因为 @run-at document-start 可能会在 head 之前执行。
    // 但通常情况下，head 元素在 document-start 时就已经可用。
    if (document.head) {
        document.head.appendChild(style);
    } else {
        // 备用方案，如果 head 尚不可用，等待 DOMContentLoaded (虽然不太可能，因为我们想尽早注入)
        document.addEventListener('DOMContentLoaded', () => {
            document.head.appendChild(style);
        });
    }
})();

(function() {
    'use strict';

    if (!unsafeWindow.__HupuCleanerMenuRegistered__) {
        const hostElement = document.createElement('div');
        hostElement.id = 'hupu-cleaner-shadow-host';
        
        hostElement.style.setProperty('pointer-events', 'none', 'important'); /* Default to none */
        hostElement.style.setProperty('display', 'none', 'important'); /* Default to hidden */

        document.body.appendChild(hostElement);

        const shadowRoot = hostElement.attachShadow({ mode: 'open' });

        const style = document.createElement('style');
        style.textContent = `
        ` + modalCssContent;
        shadowRoot.appendChild(style);

        const mountPoint = document.createElement('div');
        mountPoint.id = 'hupu-cleaner-app';
        shadowRoot.appendChild(mountPoint);

        // const app = createApp(App, { hostElement: hostElement });
        // const vm = app.mount(mountPoint);

        // 只注册用户黑名单菜单，移除图片黑名单菜单
        GM_registerMenuCommand('Blocked Users', () => {
            const vm = window.__HupuCleanerUserModal__;
            const hostElement = document.getElementById('hupu-cleaner-user-modal-host');
            hostElement.style.setProperty('pointer-events', 'auto', 'important');
            hostElement.style.setProperty('display', 'block', 'important');
            vm.showModal();
        });
        // GM_registerMenuCommand('Blocked Images', () => {
        //     const vm = window.__HupuCleanerImageModal__;
        //     const hostElement = document.getElementById('hupu-cleaner-image-modal-host');
        //     hostElement.style.setProperty('pointer-events', 'auto', 'important');
        //     hostElement.style.setProperty('display', 'block', 'important');
        //     vm.showModal();
        // });
        unsafeWindow.__HupuCleanerMenuRegistered__ = true;
    }

    initializeScript();
})();