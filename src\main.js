import { initializeScript } from './initializer.js';
import { GM_registerMenuCommand, unsafeWindow } from '$';
import './user-modal-entry.js';

// 在页面内容渲染之前，立即注入全局隐藏样式，避免闪烁。
(function() {
    const style = document.createElement('style');
    style.textContent = `
        .hupu-cleaner-hidden {
            display: none !important; /* 强制隐藏帖子和图片 */
        }
    `;
    // 确保 head 元素存在，因为 @run-at document-start 可能会在 head 之前执行。
    // 但通常情况下，head 元素在 document-start 时就已经可用。
    if (document.head) {
        document.head.appendChild(style);
    } else {
        // 备用方案，如果 head 尚不可用，等待 DOMContentLoaded (虽然不太可能，因为我们想尽早注入)
        document.addEventListener('DOMContentLoaded', () => {
            document.head.appendChild(style);
        });
    }
})();

(function() {
    'use strict';

    if (!unsafeWindow.__HupuCleanerMenuRegistered__) {
        // 注册用户黑名单菜单
        GM_registerMenuCommand('Blocked Users', () => {
            const userModal = window.__HupuCleanerUserModal__;
            const hostElement = document.getElementById('hupu-cleaner-user-modal-host');
            hostElement.style.setProperty('pointer-events', 'auto', 'important');
            hostElement.style.setProperty('display', 'block', 'important');
            userModal.showModal();
        });

        unsafeWindow.__HupuCleanerMenuRegistered__ = true;
    }

    initializeScript();
})();