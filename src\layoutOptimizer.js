// 布局优化模块 - 调整帖子列表行距使其更紧凑

// 检查用户设置并自动应用紧凑布局
export function initCompactLayout() {
    const setting = localStorage.getItem('hupu-cleaner-compact-layout');
    // 默认启用紧凑布局，只有明确设置为 'disabled' 时才不启用
    if (setting !== 'disabled') {
        applyCompactLayout();
        // 如果是第一次使用，设置默认值
        if (!setting) {
            localStorage.setItem('hupu-cleaner-compact-layout', 'enabled');
        }
    }
}

// 应用紧凑布局样式
export function applyCompactLayout() {
    // 检查是否已经注入了紧凑布局样式
    if (document.getElementById('hupu-compact-layout-style')) {
        return; // 已经注入过，直接返回
    }

    // 创建样式元素
    const style = document.createElement('style');
    style.id = 'hupu-compact-layout-style';
    style.textContent = `
        /* 紧凑帖子列表样式 - 更激进的压缩 */
        .bbs-sl-web-post-body {
            margin-bottom: 4px !important; /* 大幅减少帖子间距 */
            padding: 4px 8px !important; /* 大幅减少内边距 */
            line-height: 1.2 !important; /* 减少整体行高 */
        }

        .bbs-sl-web-post-layout {
            padding: 2px 0 !important; /* 大幅减少布局内边距 */
            line-height: 1.1 !important; /* 更紧凑的行高 */
        }

        /* 调整帖子标题行距 */
        .post-title {
            margin-bottom: 2px !important; /* 大幅减少标题下边距 */
            line-height: 1.2 !important; /* 更紧凑的标题行高 */
        }

        .post-title .p-title {
            font-size: 13px !important; /* 减小字体 */
            line-height: 1.2 !important; /* 更紧凑的行高 */
        }

        /* 调整帖子数据区域 */
        .post-datum {
            font-size: 11px !important; /* 更小的数据字体 */
            margin: 1px 0 !important; /* 大幅减少上下边距 */
            line-height: 1.1 !important; /* 更紧凑的行高 */
        }

        /* 调整作者和时间区域 */
        .post-auth, .post-time {
            font-size: 11px !important; /* 更小的字体 */
            line-height: 1.1 !important; /* 更紧凑的行高 */
            margin: 1px 0 !important; /* 大幅减少边距 */
        }

        /* 调整页面图标 */
        .page-icon {
            font-size: 10px !important; /* 更小的页面图标字体 */
            margin-left: 2px !important; /* 减少左边距 */
            line-height: 1.1 !important; /* 更紧凑的行高 */
        }

        .page-icon-item {
            font-size: 10px !important; /* 更小的页面图标项字体 */
        }

        /* 调整亮点图标 */
        .light-icon {
            width: 10px !important; /* 更小的图标尺寸 */
            height: 10px !important;
            margin: 0 1px !important; /* 减少图标边距 */
        }

        /* 调整视频图标 */
        .bbs-sl-web-post-shipin {
            font-size: 10px !important; /* 更小的视频图标 */
        }

        /* 调整iconfont图标 */
        .iconfont {
            font-size: 10px !important; /* 更小的图标字体 */
        }

        /* 如果存在帖子列表容器，也进行优化 */
        ul {
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1.1 !important; /* 更紧凑的列表行高 */
        }

        /* 针对虎扑特定的列表样式优化 */
        .bbs-sl-web-post-body:hover {
            background-color: #f8f9fa !important; /* 保持悬停效果但使用更淡的颜色 */
        }

        /* 确保所有链接也使用紧凑样式 */
        .bbs-sl-web-post-body a {
            line-height: 1.2 !important;
        }
    `;
    
    // 将样式添加到页面头部
    document.head.appendChild(style);
}

// 移除紧凑布局样式（如果需要恢复原始布局）
export function removeCompactLayout() {
    const style = document.getElementById('hupu-compact-layout-style');
    if (style) {
        style.remove();
    }
}

// 切换紧凑布局（开启/关闭）
export function toggleCompactLayout() {
    const style = document.getElementById('hupu-compact-layout-style');
    if (style) {
        removeCompactLayout();
        return false; // 返回当前状态：已关闭
    } else {
        applyCompactLayout();
        return true; // 返回当前状态：已开启
    }
}
