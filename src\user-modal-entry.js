// 该文件用于挂载 Blocked Users 弹窗
import { createApp } from 'vue';
import UserBlockModal from './UserBlockModal.vue';
import modalCssContent from './modal.css?inline';

(function() {
    const hostElement = document.createElement('div');
    hostElement.id = 'hupu-cleaner-user-modal-host';
    hostElement.style.setProperty('pointer-events', 'none', 'important');
    hostElement.style.setProperty('display', 'none', 'important');
    document.body.appendChild(hostElement);
    const shadowRoot = hostElement.attachShadow({ mode: 'open' });
    const style = document.createElement('style');
    style.textContent = modalCssContent;
    shadowRoot.appendChild(style);
    const mountPoint = document.createElement('div');
    mountPoint.id = 'hupu-cleaner-user-modal-app';
    shadowRoot.appendChild(mountPoint);
    const app = createApp(UserBlockModal, { hostElement });
    const vm = app.mount(mountPoint);
    window.__HupuCleanerUserModal__ = vm;
})();
